# 🔍 Guide d'Utilisation - Extreme Search

## ✅ Problème Résolu

L'erreur "an error occurred" que vous avez rencontrée a été **corrigée** ! Voici les améliorations apportées :

### 🛠️ Corrections Appliquées

1. **⏱️ Timeouts ajoutés** : 
   - Recherche web : 30 secondes max
   - Récupération de contenu : 20 secondes max

2. **🔒 Validation des données** :
   - Filtrage des URLs invalides
   - Vérification du contenu avant traitement
   - Limitation à 5 URLs max par requête

3. **📊 Limitation des requêtes** :
   - Maximum 3 plans de recherche (au lieu de 5)
   - Évite la surcharge de l'API Exa

4. **🛡️ Gestion d'erreur améliorée** :
   - Messages d'erreur détaillés
   - Fallback sur les résultats originaux
   - Logs de débogage complets

## 🚀 Comment Utiliser Extreme Search

### **Méthode 1 : Mode Groupe "Extreme"**
1. Sélectionnez le groupe **"Extreme"** dans l'interface
2. Posez votre question directement
3. L'outil s'active automatiquement

### **Méthode 2 : Mode Web avec demande explicite**
1. Restez dans le groupe **"Web"**
2. Utilisez des phrases comme :
   - "Fais une recherche extrême sur..."
   - "Recherche approfondie sur..."
   - "Analyse complète de..."

## 📝 Exemples d'Utilisation

### 🤖 Intelligence Artificielle
```
"Fais une recherche extrême sur les dernières innovations en IA générative en 2024"
```

### 📈 Analyse de Marché
```
"Recherche approfondie sur les tendances du marché immobilier français en 2024"
```

### 🔬 Recherche Académique
```
"Analyse complète des études récentes sur l'impact du télétravail"
```

### 📰 Actualités
```
"Recherche extrême sur les dernières nouvelles en cryptomonnaies"
```

## 🎯 Ce que Fait l'Extreme Search

1. **📋 Planification** : Crée un plan de recherche structuré
2. **🔍 Recherches multiples** : Exécute 3-5 requêtes ciblées
3. **📚 Collecte de sources** : Rassemble les informations de qualité
4. **📖 Lecture approfondie** : Récupère le contenu complet des pages
5. **✍️ Synthèse** : Analyse et rédige une réponse avec citations

## 🔧 Fonctionnalités Techniques

### **API Utilisée**
- **Exa AI** : Moteur de recherche avancé
- **Recherche par mots-clés** et **contenu complet**
- **Catégories spécialisées** : news, research, company, etc.

### **Limitations de Sécurité**
- ⏱️ **Timeout automatique** : Évite les blocages
- 🔢 **Limite de résultats** : 5 par recherche
- 📊 **Plans limités** : Maximum 3 sujets de recherche
- 🛡️ **Validation des URLs** : Seulement les liens valides

## 🎨 Interface Utilisateur

Pendant la recherche, vous verrez :
- 📊 **Statut en temps réel** : "Planning research", "Searching...", etc.
- 🔍 **Requêtes générées** : Les questions automatiques créées
- 📄 **Sources collectées** : Liens vers les pages trouvées
- 📝 **Contenu lu** : Aperçu du contenu récupéré
- ✅ **Résultat final** : Synthèse complète avec citations

## 💡 Conseils pour de Meilleurs Résultats

### ✅ **Bonnes Pratiques**
- Soyez **spécifique** dans vos questions
- Mentionnez **l'année** ou "récent" pour l'actualité
- Précisez le **domaine** : tech, finance, santé, etc.
- Demandez des **comparaisons** pour plus de profondeur

### ❌ **À Éviter**
- Questions trop vagues ou générales
- Demandes sans contexte temporel
- Sujets trop larges sans focus

### 🎯 **Exemple Optimal**
```
"Fais une recherche extrême sur les dernières innovations en intelligence artificielle générative en 2024, en comparant GPT-4, Claude et Gemini sur leurs capacités de raisonnement et leurs applications pratiques"
```

## 🔍 Résultat Attendu

Vous obtiendrez :
- **📖 Introduction** contextuelle
- **🔍 Sections détaillées** avec sous-titres
- **📊 Comparaisons** et analyses
- **🔗 Citations complètes** pour chaque fait
- **📝 Conclusion** synthétique
- **📚 Sources multiples** : web, académique, actualités

## 🆘 En Cas de Problème

Si vous rencontrez encore des erreurs :
1. **Attendez 1-2 minutes** avant de réessayer
2. **Simplifiez votre question** si elle est trop complexe
3. **Vérifiez votre connexion internet**
4. **Essayez le mode Web normal** en alternative

L'extreme search est maintenant **plus robuste** et **plus fiable** ! 🎉
